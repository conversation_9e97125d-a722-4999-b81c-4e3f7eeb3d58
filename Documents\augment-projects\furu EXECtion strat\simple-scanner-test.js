#!/usr/bin/env node

/**
 * Simple scanner test to debug individual scanners
 */

require('dotenv').config();
const { ethers } = require('ethers');

async function testBasicConnectivity() {
  console.log('🔧 Testing basic connectivity...');
  
  try {
    // Test environment variables
    console.log('Environment check:');
    console.log('- ALCHEMY_API_KEY:', process.env.ALCHEMY_API_KEY ? 'Present' : 'Missing');
    console.log('- MAINNET_RPC_URL:', process.env.MAINNET_RPC_URL ? 'Present' : 'Missing');
    
    if (!process.env.ALCHEMY_API_KEY || !process.env.MAINNET_RPC_URL) {
      throw new Error('Missing required environment variables');
    }
    
    // Test network connection
    console.log('\nTesting network connection...');
    const provider = new ethers.JsonRpcProvider(process.env.MAINNET_RPC_URL);
    const blockNumber = await provider.getBlockNumber();
    console.log('✅ Network connected - Block:', blockNumber);
    
    const gasPrice = await provider.getFeeData();
    const gasPriceGwei = Number(ethers.formatUnits(gasPrice.gasPrice, 'gwei'));
    console.log('✅ Gas price:', gasPriceGwei.toFixed(1), 'gwei');
    
    return true;
  } catch (error) {
    console.error('❌ Basic connectivity failed:', error.message);
    return false;
  }
}

async function testCrossDexScanner() {
  console.log('\n🔄 Testing Cross-DEX Arbitrage Scanner...');
  
  try {
    const { CrossDexArbitrageScanner } = require('./alpha-scanner/scanner/cross_dex_arbitrage_scanner');
    console.log('✅ Scanner module loaded');
    
    const scanner = new CrossDexArbitrageScanner('ethereum');
    console.log('✅ Scanner instance created');
    
    console.log('🔍 Executing scanner...');
    const opportunities = await scanner.execute();
    
    console.log('✅ Scanner execution completed');
    console.log(`📊 Opportunities found: ${opportunities.length}`);
    
    if (opportunities.length > 0) {
      console.log('\n🎯 Sample opportunities:');
      opportunities.slice(0, 3).forEach((opp, i) => {
        console.log(`${i + 1}. ${opp.strategyName}: $${opp.profitUSD.toFixed(2)}`);
        if (opp.pair) console.log(`   Pair: ${opp.pair}`);
        if (opp.buyDex && opp.sellDex) console.log(`   Route: ${opp.buyDex} → ${opp.sellDex}`);
      });
    }
    
    return opportunities;
  } catch (error) {
    console.error('❌ Cross-DEX scanner failed:', error.message);
    console.error('Stack trace:', error.stack);
    return [];
  }
}

async function testLiquidationScanner() {
  console.log('\n🏥 Testing Liquidation Scanner...');
  
  try {
    const { LiquidationScanner } = require('./alpha-scanner/scanner/liquidation_scanner');
    console.log('✅ Scanner module loaded');
    
    const scanner = new LiquidationScanner('ethereum');
    console.log('✅ Scanner instance created');
    
    console.log('🔍 Executing scanner...');
    const opportunities = await scanner.execute();
    
    console.log('✅ Scanner execution completed');
    console.log(`📊 Opportunities found: ${opportunities.length}`);
    
    if (opportunities.length > 0) {
      console.log('\n🎯 Sample opportunities:');
      opportunities.slice(0, 3).forEach((opp, i) => {
        console.log(`${i + 1}. ${opp.strategyName}: $${opp.profitUSD.toFixed(2)}`);
        if (opp.protocol) console.log(`   Protocol: ${opp.protocol}`);
        if (opp.userAddress) console.log(`   Target: ${opp.userAddress.substring(0, 10)}...`);
      });
    }
    
    return opportunities;
  } catch (error) {
    console.error('❌ Liquidation scanner failed:', error.message);
    console.error('Stack trace:', error.stack);
    return [];
  }
}

async function testYieldArbitrageScanner() {
  console.log('\n💰 Testing Yield Arbitrage Scanner...');
  
  try {
    const { YieldArbitrageScanner } = require('./alpha-scanner/scanner/yield_arbitrage_scanner');
    console.log('✅ Scanner module loaded');
    
    const scanner = new YieldArbitrageScanner('ethereum');
    console.log('✅ Scanner instance created');
    
    console.log('🔍 Executing scanner...');
    const opportunities = await scanner.execute();
    
    console.log('✅ Scanner execution completed');
    console.log(`📊 Opportunities found: ${opportunities.length}`);
    
    if (opportunities.length > 0) {
      console.log('\n🎯 Sample opportunities:');
      opportunities.slice(0, 3).forEach((opp, i) => {
        console.log(`${i + 1}. ${opp.strategyName}: $${opp.profitUSD.toFixed(2)}`);
        if (opp.tokenSymbol) console.log(`   Token: ${opp.tokenSymbol}`);
        if (opp.highRateProtocol && opp.lowRateProtocol) {
          console.log(`   Route: ${opp.highRateProtocol} (${opp.highRate.toFixed(2)}%) → ${opp.lowRateProtocol} (${opp.lowRate.toFixed(2)}%)`);
        }
      });
    }
    
    return opportunities;
  } catch (error) {
    console.error('❌ Yield arbitrage scanner failed:', error.message);
    console.error('Stack trace:', error.stack);
    return [];
  }
}

async function testFlashLoanRefinanceScanner() {
  console.log('\n🏦 Testing Flash Loan Refinance Scanner...');
  
  try {
    const { FlashLoanRefinanceScanner } = require('./alpha-scanner/scanner/flash_loan_refinance_scanner');
    console.log('✅ Scanner module loaded');
    
    const scanner = new FlashLoanRefinanceScanner('ethereum');
    console.log('✅ Scanner instance created');
    
    console.log('🔍 Executing scanner...');
    const opportunities = await scanner.execute();
    
    console.log('✅ Scanner execution completed');
    console.log(`📊 Opportunities found: ${opportunities.length}`);
    
    if (opportunities.length > 0) {
      console.log('\n🎯 Sample opportunities:');
      opportunities.slice(0, 3).forEach((opp, i) => {
        console.log(`${i + 1}. ${opp.strategyName}: $${opp.profitUSD.toFixed(2)}`);
        if (opp.currentProtocol && opp.targetProtocol) {
          console.log(`   Refinance: ${opp.currentProtocol} → ${opp.targetProtocol}`);
        }
        if (opp.debtAmountUSD) console.log(`   Debt: $${opp.debtAmountUSD.toLocaleString()}`);
      });
    }
    
    return opportunities;
  } catch (error) {
    console.error('❌ Flash loan refinance scanner failed:', error.message);
    console.error('Stack trace:', error.stack);
    return [];
  }
}

async function main() {
  console.log('🚀 SIMPLE SCANNER TEST\n');
  console.log('═══════════════════════════════════════════════════════════');
  
  try {
    // Test basic connectivity first
    const connectivityOK = await testBasicConnectivity();
    if (!connectivityOK) {
      console.log('\n❌ Basic connectivity failed - cannot proceed');
      process.exit(1);
    }
    
    console.log('\n═══════════════════════════════════════════════════════════');
    
    // Test each scanner individually
    const allOpportunities = [];
    
    const crossDexOpps = await testCrossDexScanner();
    allOpportunities.push(...crossDexOpps);
    
    const liquidationOpps = await testLiquidationScanner();
    allOpportunities.push(...liquidationOpps);
    
    const yieldOpps = await testYieldArbitrageScanner();
    allOpportunities.push(...yieldOpps);
    
    const refinanceOpps = await testFlashLoanRefinanceScanner();
    allOpportunities.push(...refinanceOpps);
    
    // Summary
    console.log('\n═══════════════════════════════════════════════════════════');
    console.log('📊 SUMMARY');
    console.log('═══════════════════════════════════════════════════════════');
    console.log(`Total opportunities found: ${allOpportunities.length}`);
    
    if (allOpportunities.length > 0) {
      const totalProfit = allOpportunities.reduce((sum, opp) => sum + opp.profitUSD, 0);
      console.log(`Total profit potential: $${totalProfit.toFixed(2)}`);
      
      // Sort by profit and show top opportunities
      allOpportunities.sort((a, b) => b.profitUSD - a.profitUSD);
      console.log('\n🏆 TOP OPPORTUNITIES:');
      allOpportunities.slice(0, 5).forEach((opp, i) => {
        console.log(`${i + 1}. ${opp.strategyName}: $${opp.profitUSD.toFixed(2)}`);
      });
    } else {
      console.log('No profitable opportunities found (this is normal due to market efficiency)');
    }
    
    console.log('\n✅ All scanner tests completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Execute tests
if (require.main === module) {
  main();
}
